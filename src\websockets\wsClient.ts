import {HOST_ADRESS} from "../constants/urls";

type MessageEvent = { event: string; data: any };

let socket: WebSocket;
const listeners: { [key: string]: (data: any) => void } = {};

export const connectWebSocket = (userId: string, onOpenCallback: () => void) => {
    socket = new WebSocket(`wss://${HOST_ADRESS.replace("https://", "")}/ws/${userId}`);

    socket.onmessage = (event) => {
        const message: MessageEvent = JSON.parse(event.data);
        const handler = listeners[message.event];
        if (handler) handler(message.data);
    };

    socket.onclose = () => {
        console.warn("WebSocket closed");
    };

    socket.onerror = (err) => {
        console.error("WebSocket error:", err);
    };

    socket.onopen = () => {
        console.log("WebSocket opened");
        onOpenCallback();
    }
};

export const addListener = (event: string, cb: (data: any) => void) => {
    listeners[event] = cb;
};

export const sendMessage = (event: string, data: any) => {
    if (!socket) {
        console.warn("WebSocket is not connected yet. Message not sent:", event, data);
        return;
    }

    const msg = JSON.stringify({ event, data });
    if (socket.readyState === WebSocket.OPEN) {
        socket.send(msg);
    } else {
        socket.addEventListener("open", () => {
            socket.send(msg);
        }, { once: true });
    }
};