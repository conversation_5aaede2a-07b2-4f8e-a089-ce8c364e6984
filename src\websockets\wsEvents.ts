export const wsEvents = {
    //Global events broadcasted
    pointAdded: 'point_added',
    pointRemoved: 'point_removed',
    objectAdded: 'object_added',
    objectUpdated: 'object_updated',
    objectRemoved: 'object_removed',
    sessionStateChange: 'session_state_change',
    trackingUpdate: 'tracking_update',
    videoMetadata: 'video_metadata',
    trackingStarted: 'tracking_started',
    trackingStopped: 'tracking_stopped',
    trackingFinished: 'tracking_finished',

    //Responses for private messages / requests
    objectsSync: 'objects_sync', //response to get_all_objects
    frameResult: 'frame_result', //response to get_frame
    framesBatchResult: 'frames_batch_result', //response to get_frames_batch
    allPoints: 'all_points',
    stateResponse: 'state_response',
}

