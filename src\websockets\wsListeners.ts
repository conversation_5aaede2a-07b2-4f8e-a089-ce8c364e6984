import {addListener} from "./wsClient"
import {FormItem} from "../types/formItem";
import {wsEvents} from "./wsEvents";
import {ISelectionPoint} from "../types/selectionPoint";
import {SessionState} from "../enums/sessionState";
import {ObjectResult, PropagateVideoResponse} from "../types/polygons.type";
import React from "react";

export const setupWebSocketListeners = (ctx: {
    sessionStateRef: React.MutableRefObject<SessionState>;
    sessionState: SessionState;
    setSessionState: (state: SessionState) => void;
    setTrackingResult: (frameIndex: number, data: ObjectResult[]) => void;
    setTrackingResults: (trackingResults: Record<number, ObjectResult[]>) => void;
    setBatchTrackingResults: (batch: PropagateVideoResponse[]) => void;
    trackingResultsRef: React.MutableRefObject<Record<number, ObjectResult[]>>;
    setActiveItem: (idx: number) => void;
    setLabel: (label: boolean) => void;
    updateItem: (updatedItem: FormItem) => void;
    addItem: (item: FormItem) => void;
    removeItem: (id: number) => void;
    setItemImage: (id: number, file: File) => void;
    replaceItems: (list: FormItem[]) => void;
    addSelectionPoint: (point: ISelectionPoint) => void;
    removeSelectionPoint: (point: ISelectionPoint) => void;
    replaceSelectionPoints: (frames: Record<number, ISelectionPoint[]>) => void;
    drawFrame: () => void;
    currentFrameRef: React.MutableRefObject<number>;
    setCurrentFrame: (frameIndex: number) => void;
    setRFrameRate: (frameRate: number) => void;
    rFrameRateRef: React.MutableRefObject<number>;
    playerRef: React.MutableRefObject<any>;
    sendGetFrame: (frameIdx: number) => void;
    setLastDownloadedFrame: (frameIdx: number) => void;
    showError: (message: string) => void;
    sendGetFramesBatch: (frameIdx: number, batchSize: number) => Promise<void>;
    deleteObjectFromTrackingResults: (objectId: number) => void;
    removeSelectionPointsForObject: (objectId: number) => void;
}) => {
    addListener(wsEvents.objectAdded, (data: any) => {
        ctx.addItem({
            id: data.data.objectId,
            itemName: "",
            brand: "",
            link: "",
            identificationName: "Identification name",
            image: null
        });

        ctx.setActiveItem(data.data.objectId);
    })

    addListener(wsEvents.objectsSync, (data: any) => {
        const mappedItems: FormItem[] = data.map((obj: any) => ({
            id: obj.objectId,
            itemName: obj.name,
            brand: obj.brand,
            link: obj.url,
            identificationName: obj.idName,
            image: obj.image,
        }))

        ctx.replaceItems(mappedItems)
    })

    addListener(wsEvents.objectRemoved, (data: any) => {
        ctx.removeItem(data.data);
        ctx.deleteObjectFromTrackingResults(data.data);
        ctx.removeSelectionPointsForObject(data.data);
        ctx.drawFrame();
    })

    addListener(wsEvents.objectUpdated, (data: any) => {
        ctx.updateItem(data["data"])
    })

    addListener(wsEvents.frameResult, (data: any) => {
        const frameIndex = ctx.currentFrameRef.current;
        if (Array.isArray(data)) {
            ctx.setTrackingResult(frameIndex, data);
        }
        else {
            ctx.setTrackingResult(frameIndex, []);
        }
    });

    addListener(wsEvents.framesBatchResult, (data: any) => {
        const batch = data;

        if (Array.isArray(batch)) {
            ctx.setBatchTrackingResults(batch);
            const last = batch[batch.length - 1];
            ctx.setLastDownloadedFrame(last.frameIndex);
        } else {
            console.warn("Unexpected framesBatchResult structure, setting empty batch:", data);
            ctx.setBatchTrackingResults([]);
        }
    })

    addListener(wsEvents.pointAdded, (data: any) => {
        ctx.sendGetFrame(ctx.currentFrameRef.current);
        console.log("Requested the frame")

        ctx.addSelectionPoint(data.point)
    })

    addListener(wsEvents.pointRemoved, (data: any) => {
        const rawPoint = data.point;

        if (rawPoint && rawPoint.frameIndex !== undefined) {
            const point: ISelectionPoint = {
                frameIndex: rawPoint.frameIndex,
                objectId: rawPoint.objectId,
                label: rawPoint.label,
                coordinates: {
                    x: rawPoint.coordinates.x,
                    y: rawPoint.coordinates.y,
                }
            };


            ctx.sendGetFrame(ctx.currentFrameRef.current);


            ctx.removeSelectionPoint(point);
        } else {
            console.warn("Invalid pointRemoved structure", data);
        }
    });

    addListener(wsEvents.sessionStateChange, (data: any) => {
        console.log('sessionStateChange', data);
        if (
            data &&
            data.sessionState !== undefined &&
            Object.values(SessionState).includes(data.sessionState)
        ) {
            ctx.setSessionState(data["sessionState"] as SessionState);
            ctx.sessionStateRef.current = data["sessionState"] as SessionState;
        } else {
            console.warn("Invalid sessionStateChange structure:", data);
        }
    })

    addListener(wsEvents.trackingUpdate, (data: any) => {
        const frameIndex = data.data.frameIndex;
        const timeInSeconds = frameIndex / ctx.rFrameRateRef.current

        if (data.data && Array.isArray(data.data.results)) {
            ctx.playerRef.current.currentTime(timeInSeconds);
            ctx.setTrackingResult(frameIndex, data.data.results);
            ctx.setCurrentFrame(frameIndex);
            ctx.currentFrameRef.current = frameIndex;
        } else {
            console.warn("Unexpected trackingUpdate structure:", data);
            ctx.setTrackingResult(frameIndex, []);
        }
    })

    addListener(wsEvents.videoMetadata, (data: any) => {
        console.log('videoMetadata', data);
        ctx.setRFrameRate(data["video_metadata"]["rFrameRate"])
        ctx.rFrameRateRef.current = data["video_metadata"]["rFrameRate"];
    })

    addListener(wsEvents.allPoints, (data: any) => {
        try {
            if (data) {
                const rawPoints = data;
                const converted: Record<number, ISelectionPoint[]> = {};

                Object.entries(rawPoints).forEach(([key, value]) => {
                    if (Array.isArray(value)) {
                        converted[Number(key)] = value as ISelectionPoint[];
                    } else {
                        console.warn(`Invalid value for key ${key}: expected array`, value);
                    }
                });

                ctx.replaceSelectionPoints(converted);
            } else {
                console.warn("Invalid data for allPoints: ", data);
            }
        } catch (err) {
            console.error("Failed to process allPoints data:", err);
            console.warn("Raw data:", data);
        }
    });

    addListener(wsEvents.stateResponse, (data: any) => {
        console.log('stateResponse', data);
        if (
            data &&
            data.session_state !== undefined &&
            Object.values(SessionState).includes(data.session_state)
        ) {
            ctx.setSessionState(data["session_state"] as SessionState);
            ctx.sessionStateRef.current = data["session_state"] as SessionState;
        } else {
            console.warn("Invalid sessionResponse structure:", data);
        }
    })

    addListener(wsEvents.trackingStarted, (data: any) => {
        ctx.setTrackingResults({})
        ctx.trackingResultsRef.current = {}
        ctx.setSessionState(SessionState.TRACKING)
        ctx.sessionStateRef.current = SessionState.TRACKING;
    })

    addListener(wsEvents.trackingStopped, () => {
        ctx.setSessionState(SessionState.IDLE);
        ctx.sessionStateRef.current = SessionState.IDLE;

        if (ctx.playerRef.current) {
            ctx.playerRef.current.currentTime(0);
            ctx.setCurrentFrame(0);
            ctx.currentFrameRef.current = 0;
        }

        const frame0Results = ctx.trackingResultsRef.current[0] || [];

        ctx.setTrackingResults({ 0: frame0Results });
        ctx.trackingResultsRef.current = { 0: frame0Results };

        if (!frame0Results.length) {
            setTimeout(() => {
                ctx.sendGetFrame(0);
            }, 100);
        } else {
            ctx.drawFrame();
        }
    })

    addListener(wsEvents.trackingFinished, (data: any) => {
        ctx.setSessionState(SessionState.IDLE)
        ctx.sessionStateRef.current = SessionState.IDLE;

        const requestedBatchStarts = new Set<number>();
        const preloadEndFrame = ctx.currentFrameRef.current + 100;
        const batchSize = 15;

        for (let batchStart = 0; batchStart <= preloadEndFrame; batchStart += batchSize) {
            if (!requestedBatchStarts.has(batchStart)) {
                requestedBatchStarts.add(batchStart);
                try {
                    ctx.sendGetFramesBatch(batchStart, batchSize);
                } catch (error) {
                    console.error(error);
                }
            }
        }
    })

    addListener("error", (data: any) => {
        ctx.showError(data.message || "Unknown WebSocket error");
    })
}
