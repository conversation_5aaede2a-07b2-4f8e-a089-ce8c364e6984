import React, { useEffect } from "react";
import { <PERSON>ton, CircularProgress } from "@mui/material";
import CloudSyncIcon from "@mui/icons-material/CloudSync";
import styles from './controlPanel.module.scss';
import { formatDate } from "../../middleware/formatDate";
import MessageAlert from "../messageAlert/MessageAlert";
import { FormData} from "../../types/formData";
import {SessionState} from "../../enums/sessionState";

interface ControlPanelProps {
    sessionState: SessionState | null;
    sendStartTracking: (frameIdx: number) => void;
    sendStopTracking: () => void;
    setIsSyncModalOpen: (isOpen: boolean) => void;
    handleToggleVM: () => void;
    buttonStyles: object;
    data: any;
    vmError: any;
    vmStatus?: string;
    vmIsLoading: boolean;
    startMutation: {
        status: string;
        mutate: () => void;
    };
    stopMutation: {
        status: string;
        mutate: () => void;
    };
    setFormData: React.Dispatch<React.SetStateAction<FormData>>;
    formData: FormData;
    currentFrame: number;
}


export const ControlPanel = ({
    sessionState,
    sendStartTracking,
    sendStopTracking,
    setIsSyncModalOpen,
    handleToggleVM,
    buttonStyles,
    data,
    vmError,
    vmStatus,
    vmIsLoading,
    startMutation,
    stopMutation,
    formData,
    setFormData,
    currentFrame,
}: ControlPanelProps) => {
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setFormData(prevData => ({
            ...prevData,
            [name]: value
        }));
    };

    useEffect(() => {
        if (data?.values) {
            const getValue = (alias: string) => {
                const field = data.values.find((item: any) => item.alias === alias);
                return field ? field.value : '';
            };

            setFormData(prev => ({
                ...prev,
                date: getValue("date"),
                title: getValue("pageTitle"),
                client: getValue("client"),
                videographer: getValue("videographer")
            }));
        }
    }, [data, setFormData]);

    const handleStartTracking = () => {
        sendStartTracking(currentFrame);
    };

    const handleStopTracking = () => {
        sendStopTracking();
    }

    return (
        <>
            {vmError && <MessageAlert type="error" message={`Error: ${vmError.message}`} />}
            <section className={styles.controlPanel}>
                <div className={styles.mlMachineStatusContainer}>
                    <div className={styles.mlMachineStatus}>
                        {(vmIsLoading ||
                            startMutation.status === 'pending' ||
                            stopMutation.status === 'pending') ? (
                            <CircularProgress size={24} color="inherit" />
                        ) : (
                            !vmIsLoading &&
                            !vmError && (
                                <>
                                    {vmStatus ? (
                                        <>
                                            <p>Connected to AI worker</p>
                                            <div className={styles.greenDot}></div>
                                        </>
                                    ) : (
                                        <>
                                            <p>No connection...</p>
                                            <div className={styles.redDot}></div>
                                        </>
                                    )}
                                </>
                            )
                        )}
                    </div>
                </div>
                <div className={styles.topArea}>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>DATE</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="date"
                                value={formData.date}
                                onChange={handleInputChange}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>TITLE</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="title"
                                value={formData.title}
                                onChange={handleInputChange}
                            />
                        </div>
                    </div>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>CLIENT</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="client"
                                value={formData.client}
                                onChange={handleInputChange}
                            />
                        </div>
                    </div>
                    <div className={styles.infoElement}>
                        <div className={styles.header}>
                            <p>VIDEOGRAPHER</p>
                        </div>
                        <div className={styles.infoData}>
                            <input
                                type="text"
                                name="videographer"
                                value={formData.videographer}
                                onChange={handleInputChange}
                            />
                        </div>
                    </div>
                </div>
                <div className={styles.buttonContainer}>
                    <Button
                        variant="contained"
                        onClick={sessionState === SessionState.TRACKING ? handleStopTracking : handleStartTracking}
                        disabled={(sessionState !== SessionState.IDLE && sessionState !== SessionState.TRACKING)}
                        sx={buttonStyles}
                    >
                        {sessionState === SessionState.TRACKING ? "Stop Tracking" : "Start Tracking"}
                    </Button>
                </div>
            </section>
        </>
    );
};
