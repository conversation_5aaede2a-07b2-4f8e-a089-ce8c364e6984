import { useState } from "react";
import { FormItem } from "../types/formItem"

export const useFormItems = (initialItems: FormItem[] = []) => {
    const [items, setItems] = useState<FormItem[]>(initialItems);

    const updateItem = (raw: any) => {
        const updatedItem: FormItem = {
            id: raw.objectId,
            itemName: raw.name,
            identificationName: raw.idName,
            brand: raw.brand,
            link: raw.url,
            image: raw.image ?? null
        };

        setItems((prev) =>
            prev.map((item) =>
                item.id === updatedItem.id ? { ...item, ...updatedItem } : item
            )
        );
    };

    const setItemImage = (id: number, file: File) => {
        setItems((prev) =>
            prev.map((item) =>
                item.id === id ? { ...item, image: file } : item
            )
        );
    };


    const addItem = (item: FormItem) => setItems((prev) => [...prev, item]);
    const removeItem = (id: number) =>
        setItems((prev) => prev.filter((item) => item.id !== id));

    const replaceItems = (newItems: FormItem[]) => setItems(newItems);

    return { items, setItems, updateItem, addItem, removeItem, setItemImage, replaceItems };
};