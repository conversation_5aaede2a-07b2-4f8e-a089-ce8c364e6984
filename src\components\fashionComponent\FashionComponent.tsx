import styles from './fashionComponent.module.scss';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from "@mui/icons-material/Edit";
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import useDeleteDocument from '../../api/useDeleteDocument';
import DoneIcon from '@mui/icons-material/Done';
import { Button, TextareaAutosize, TextField, Typography } from '@mui/material';
import { useState } from 'react';
import SidebarForm from './SidePanel';
import DeleteConfirmationModal from '../deleteConfirmationModal/DeleteConfirmationModal';
import SaveConfirmationModal from '../saveConfirmationModal/SaveConfirmationModal';
import useUpdateFashionItem from '../../api/useUpdateFashionItem';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';

const commonInputSx = {
  '& .MuiOutlinedInput-root': {
    color: '#4D4D4D',
    fontFamily: 'Poppins, sans-serif',
    fontSize: '16px',
    backgroundColor: 'transparent',
    '& fieldset': {
      borderColor: '#808080',
    },
    '&:hover fieldset': {
      borderColor: '#808080',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#4D4D4D',
    },
  },
};

const FashionComponent = ({ item }: any) => {
  const {
    properties: { description, shopPagePhoto, addItem },
  } = item;

  const [title, setTitle] = useState(item.name);
  const [urlSlug, setUrlSlug] = useState(item.route.path);
  const [desc, setDesc] = useState(description);
  const [photographer, setPhotographer] = useState(item.properties.photographer);

  const [fullName, setFullName] = useState("");
  const [date, setDate] = useState(item.properties.date);
  const [city, setCity] = useState(item.properties.location);
  const [mail, setMail] = useState(item.properties.mail);
  const [ig, setIg] = useState("");
  const [fb, setFb] = useState(item.properties.facebook);
  const [tiktok, setTiktok] = useState("");
  const [other, setOther] = useState(item.properties.socials);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState<boolean>(false);
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const [isUploadModalOpen, setUploadModalOpen] = useState(false);

  const { deleteDocument } = useDeleteDocument();

  const handleOpenUploadModal = () => setUploadModalOpen(true);
  const handleCloseUploadModal = () => setUploadModalOpen(false);

  const { updateFashionItem } = useUpdateFashionItem();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const handleDelete = async () => {
    try {
      await deleteDocument(item.id);
      queryClient.invalidateQueries({ queryKey: ["addShopPage"] });
      navigate("/photos-page");
    } catch (error) {
      console.error("Error deleting photo:", error);
    }
  };

  const handleOpenSidebar = () => setSidebarOpen(true);
  const handleCloseSidebar = () => setSidebarOpen(false);

  const handleSave = async () => {
    if (!item.id) return;
    const updatedData = {
      title: photographer,
      description: description,
      photographer: photographer,
    };
    try {
      await updateFashionItem(item.id, updatedData);
      setIsSaveModalOpen(true);
      queryClient.invalidateQueries({ queryKey: ["addShopPage"] });
    } catch (error) {
      console.error("Error updating photo item:", error);
    }
  };

  return (
    <>
      <div className={styles.fashionComponent}>
        <div className={styles.header}>
          <div>
            <div className={`${styles.button}`} onClick={() => setIsDeleteModalOpen(true)}>
              <DeleteIcon /> <p>Delete</p>
            </div>
            <div className={`${styles.button}`} onClick={() => window.open("https://traffique.astroid.com.pl" + item.route.path, "_blank")}>
              <RemoveRedEyeIcon /> <p>Preview</p>
            </div>
          </div>
          <div>
            <div className={`${styles.button} ${styles.save}`} onClick={handleSave}>
              <DoneIcon /> <p>Save</p>
            </div>
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles.containerWithImage}>
            <div className={styles.leftSide}>
              <img
                src={'https://duetprodumbraco.azurewebsites.net/' + shopPagePhoto[0].url}
                alt="Fashion"
              />
            </div>
            <div className={styles.rightSide}>
              <Typography variant="body1">
                Title
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
              />
              <Typography variant="body1">
                URL Slug
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={urlSlug}
                onChange={(e) => setUrlSlug(e.target.value)}
              />
              <Typography variant="body1">
                Description
              </Typography>
              <TextareaAutosize
                minRows={6}
                value={item.properties.description}
                onChange={(e) => setDesc(e.target.value)}
              />
              <Typography variant="body1">
                Photographer
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                sx={{ mb: 2, ...commonInputSx }}
                value={item.properties.photographer}
                onChange={(e) => setPhotographer(e.target.value)}
              />
            </div>
          </div>
          <div className={styles.bottom}>
            <TextField
              placeholder='Full Name'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
            />
            <TextField
              placeholder='Date'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={item.properties.date}
              onChange={(e) => setDate(e.target.value)}
            />
            <TextField
              placeholder='City'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={item.properties.location}
              onChange={(e) => setCity(e.target.value)}
            />
            <TextField
              placeholder='Mail'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={item.properties.mail}
              onChange={(e) => setMail(e.target.value)}
            />
            <TextField
              placeholder='@IG'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={ig}
              onChange={(e) => setIg(e.target.value)}
            />
            <TextField
              placeholder='@FB'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={item.properties.facebook}
              onChange={(e) => setFb(e.target.value)}
            />
            <TextField
              placeholder='@TIKTOK'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={tiktok}
              onChange={(e) => setTiktok(e.target.value)}
            />
            <TextField
              placeholder='@Other'
              fullWidth
              variant="outlined"
              sx={{ mb: 2, ...commonInputSx }}
              value={item.properties.socials}
              onChange={(e) => setOther(e.target.value)}
            />
          </div>
          <div className={styles.itemsConteiner}>
            <p className={styles.title}>ADD ITEM</p>
            <div className={styles.item}>
              <div className={styles.leftSide}>
                <p>Item</p>
              </div>
              <div className={styles.rightSide}>
                <Button onClick={handleOpenSidebar}><EditIcon /></Button>
                <Button><DeleteIcon /></Button>
              </div>
            </div>
            <div className={styles.addItem}>
              Add Item
            </div>
          </div>
        </div>
      </div>
      <SaveConfirmationModal
        open={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
      />
      <DeleteConfirmationModal
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
      />
      <SidebarForm open={isSidebarOpen} onClose={handleCloseSidebar} />
    </>
  );
};

export default FashionComponent;