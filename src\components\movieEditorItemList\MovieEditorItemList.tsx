import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>ield, Typography } from "@mui/material";
import RemoveIcon from '@mui/icons-material/Remove';
import AddIcon from '@mui/icons-material/Add';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import styles from './movieEditorItemList.module.scss';
import { DetectionObject } from "../../DetectionObject";
import { maskColors } from "../../constants/colors";
import DeleteIcon from '@mui/icons-material/Delete';
import MovieEditorItemForm from "../MovieEditorItemForm/MovieEditorItemForm";
import {FormItem} from "../../types/formItem";
import {useRemoveObject} from "../../api/websockets/useRemoveObject";
import { ISelectionPoint } from "../../types/selectionPoint";

interface MovieEditorItemListProps {
  canAddNewObject: boolean;
  activeItem: number;
  addItem: () => void;
  setActiveItem: (index: number) => void;
  setLabel: React.Dispatch<React.SetStateAction<boolean>>;
  buttonStyles: object;
  label: boolean;
  posterImageUrls: { [key: number]: Blob | string };
  items: FormItem[];
  updateItem: (item: FormItem) => void;
  removeItem: (id: number) => void;
  setItemImage: (id: number, file: File) => void;
  handleSave: () => void;
  sessionId: string;
  sendRemovePoint: (point: ISelectionPoint) => void;
  selectionPointsRef: React.MutableRefObject<Record<number, ISelectionPoint[]>>
  currentFrame: number;
}

interface UploadThumbnailProps {
  posterImageUrl?: Blob | string;
  onFileSelect?: (file: File) => void;
}

const UploadThumbnail = ({ posterImageUrl, onFileSelect }: UploadThumbnailProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [displayUrl, setDisplayUrl] = useState<string>("");

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (posterImageUrl) {
      if (posterImageUrl instanceof Blob) {
        const url = URL.createObjectURL(posterImageUrl);
        setDisplayUrl(url);
        return () => {
          URL.revokeObjectURL(url);
        };
      } else {
        setDisplayUrl(posterImageUrl);
      }
    }
  }, [posterImageUrl]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
      if (onFileSelect) {
        onFileSelect(file);
      }
    }
  };

  const handleThumbnailClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      setSelectedFile(file);
      if (onFileSelect) {
        onFileSelect(file);
      }
    }
  };

  return (
    <div
      className={`${styles.thumbnailArea} ${dragActive ? styles.active : ""}`}
      onClick={handleThumbnailClick}
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {selectedFile ? (
        <>
          <img src={URL.createObjectURL(selectedFile)} alt="Thumbnail" />
          <FileUploadIcon className={styles.uploadIcon} />
        </>
      ) : posterImageUrl ? (
        <>
          <img src={displayUrl} alt="Default Thumbnail" />
          <FileUploadIcon className={styles.uploadIcon} />
        </>
      ) : (
        <div className={styles.icon}>
          <AddIcon />
          <p>Choose</p>
        </div>
      )}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        style={{ display: "none" }}
        accept="image/*"
      />
    </div>
  );
};

const MovieEditorItemList = ({
  canAddNewObject,
  activeItem,
  addItem,
  setActiveItem,
  setLabel,
  label,
  buttonStyles,
  posterImageUrls,
  items,
  updateItem,
  setItemImage,
  handleSave,
  sessionId,
  sendRemovePoint,
  selectionPointsRef,
  currentFrame
}: MovieEditorItemListProps) => {

  const {sendRemoveObject} = useRemoveObject();
  const [localIdentificationName, setLocalIdentificationName] = useState<Record<number, string>>("");

  return (
    <div className={styles.movieEditorItemList}>
      {items.map((item, index) => (
        <div key={item.id} data-debug-id={item.id}>
          <div className={styles.divider}>
            <div className={styles.dividerTxt}>
              <div className={styles.inputElement}>
                <input
                    type="text"
                    value={localIdentificationName[item.id]}
                    onChange={(e) =>
                        setLocalIdentificationName({...localIdentificationName ,[item.id]: e.target.value})
                    }
                    onBlur={() => updateItem({ ...item, identificationName: localIdentificationName[item.id] })}
                />
              </div>
            </div>
            <div className={styles.line}></div>
          </div>
          <div className={styles.item}>
            <div
              className={`${styles.leftArea} ${item.id === activeItem? styles.activeLeftArea : ""
                }`}
              onClick={() => setActiveItem(item.id)}
            >
              <div
                className={styles.trashIconWrapper}
                onClick={() => {
                  sendRemoveObject(item.id); 
                  if (selectionPointsRef.current[currentFrame] === undefined) return;

                  let selectionPoints = selectionPointsRef.current[currentFrame].filter(p => p.objectId === item.id);
                  
                  if (selectionPoints !== undefined) {
                    for (const sp of selectionPoints) {
                      sendRemovePoint(sp);
                    }
                  }
                }}
              >
                <DeleteIcon className={styles.trashIcon} />
              </div>
              <UploadThumbnail
                posterImageUrl={posterImageUrls[item.id]}
                onFileSelect={(file: File) =>
                    (file: File) => setItemImage(item.id, file)}
              />
              {item.identificationName ?
                <Typography
                  sx={{
                    fontSize: 14,
                    fontWeight: 700,
                    textTransform: "uppercase",
                    backgroundColor: maskColors[item.id % maskColors.length],
                    padding: "4px 10px",
                    borderRadius: 1.5,
                    color: "#fff",
                    margin: "10px",
                    maxWidth: '105px',
                    overflow: 'hidden',
                  }}
                >
                  {item.identificationName}
                </Typography>
                :
                <></>
              }
              <div className={styles.buttonContainer}>
                <div
                  className={`${styles.add} ${label && item.id === activeItem ? styles.activeButton : ""
                    }`}
                  onClick={() => setLabel(true)}
                >
                  <AddIcon />
                </div>
                <div
                  className={`${styles.delete} ${!label && item.id === activeItem ? styles.activeButton : ""
                    }`}
                  onClick={() => setLabel(false)}
                >
                  <RemoveIcon />
                </div>
              </div>
            </div>
            <MovieEditorItemForm
                key={`${item.id}-${item.itemName}-${item.brand}-${item.link}`}
                item={item}
                sendUpdate={(updated) => updateItem({ ...item, ...updated })}
            />
          </div>
        </div>
      ))}
      <div className={styles.addButtonContainer}>
      <Button
        className={styles.addButton}
        variant="contained"
        startIcon={<AddIcon />}
        onClick={() => {
          addItem();
        }}
        disabled={!canAddNewObject}
        sx={buttonStyles}
      >
        Add Detection Object
      </Button>
      </div>
      <div className={styles.saveButtonContainer}>
        <div className={styles.saveButtonContainer}>
          <div
            className={`${styles.button} ${styles.save}`}
            onClick={handleSave}
          >
            <p>Save</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MovieEditorItemList;
