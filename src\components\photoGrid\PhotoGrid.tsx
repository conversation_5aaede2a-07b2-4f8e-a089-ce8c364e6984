import { useEffect, useState } from "react";
import styles from "./photoGrid.module.scss";
import { UMBRACO_ADRESS } from "../../constants/urls";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import FavoriteIcon from "@mui/icons-material/Favorite";
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder";
import { Link } from "react-router-dom";
import useGoogleAnalyticsData from "../../api/useGoogleAnalyticsData";
import useExternalLinkClicks from "../../api/useExternalLinkClicks";
import useAddToFavorite from "../../api/useAddToFavorites";
import useDeleteFavorite from "../../api/useDeleteFavorite";
import { useQueryClient } from "@tanstack/react-query";

type PhotoGridProps = {
  items: any[];
};

const PhotoGrid = ({ items }: PhotoGridProps) => {
  const [activeSort, setActiveSort] = useState<string>("Newest");
  // Nowy stan do śledzenia liczby wyświetlanych elementów
  const [visibleCount, setVisibleCount] = useState<number>(9);
  const [favorites, setFavorites] = useState<Array<boolean>>([]);

  const handleSortChange = (sortType: string) => {
    setActiveSort(sortType);
  };

  const queryClient = useQueryClient();
  const shopPageViews = useGoogleAnalyticsData();
  const externalClicks = useExternalLinkClicks(); // Użycie hooka do pobierania kliknięć
  const { addToFavorite } = useAddToFavorite(); //TODO ładowanie i UX
  const { deleteFavorite } = useDeleteFavorite(); //TODO ładowanie i UX

  console.log("🔥 Widoki stron:", shopPageViews);
  console.log("🔥 Kliknięcia linków zewnętrznych:", externalClicks);
  console.log(items);

  const transformedItems = items.map((item: any) => {
    const properties =
      item.values?.reduce((acc: any, curr: any) => {
        acc[curr.editorAlias] = curr.value;
        return acc;
      }, {}) || {};

    item.properties = properties;

    item.name = properties.models || "Untitled";

    if (!item.route) {
      item.route = {
        path: `/photos-page/${item.name.replace(/\s+/g, "-").toLowerCase()}`,
      };
    }

    // Usunięcie przedrostka `/photos-page/` (klucz musi odpowiadać temu, co mamy w danych GA)
    const cleanPath = item.route.path
      .replace("/photos-page/", "")
      .replace(/\/$/, "");

    // Przypisanie wyświetleń i kliknięć
    item.views = shopPageViews[cleanPath] || 0;
    item.clicks = externalClicks[cleanPath] || 0;

    return item;
  });

  const filteredData = transformedItems.filter(
    (item: { name: string }) => item.name !== "Untitled"
  );

  const sortedData = [...filteredData].sort((a, b) => {
    if (activeSort === "Title (A > Z)") {
      return a.name.localeCompare(b.name);
    }
    if (activeSort === "Title (Z > A)") {
      return b.name.localeCompare(a.name);
    }
    if (activeSort === "Newest") {
      return (
        new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime()
      );
    }
    if (activeSort === "Oldest") {
      return (
        new Date(a.createdDate).getTime() - new Date(b.createdDate).getTime()
      );
    }
    return 0;
  });

  useEffect(() => {
    var fav = sortedData.map((data) => data.isFavorite);
    console.log(fav);
    setFavorites(fav);
  }, [sortedData]);

  // Funkcja obsługująca ładowanie kolejnych elementów
  const handleLoadMore = () => {
    setVisibleCount((prev) => prev + 3);
  };

  const handleToggleFavorite = (
    id: string,
    index: number,
    isFavorite: boolean
  ) => {
    console.log(`Toggling favorite for item with ID ${id}: ${isFavorite}`);
    setFavorites((prev) => {
      const updated = [...prev];
      updated[index] = !isFavorite;
      console.log("updated");
      console.log(updated);
      return updated;
    });
    if (!isFavorite) {
      addToFavorite(id);
    } else {
      deleteFavorite(id);
    }
    queryClient.invalidateQueries({ queryKey: ["getFavorites"] });
    //  window.location.reload();
  };

  return (
    <div className={styles.photoGrid}>
      <div className={styles.filters}>
        <Link to={"/clique"} className={styles.newPhoto}>
          + New photo
        </Link>
        <Link to={"/clique-add-video"} className={`${styles.videos}`}>
          + New video
        </Link>
      </div>
      <div className={styles.sortOptions}>
        <p>Sort by:</p>
        {["Title (A > Z)", "Title (Z > A)", "Newest", "Oldest"].map(
          (sortType) => (
            <span
              key={sortType}
              className={`${styles.sortOption} ${
                activeSort === sortType ? styles.active : ""
              }`}
              onClick={() => handleSortChange(sortType)}
            >
              {sortType.replace("-", " ")}
            </span>
          )
        )}
      </div>
      <div className={styles.mainContent}>
        <div className={styles.photoGrid}>
          {sortedData.slice(0, visibleCount).map((item: any, index: number) => {
            const { properties } = item;
            const shopPagePhoto = properties.shopPagePhoto;
            const name = item.name;
            const isFavorite = favorites[index];

            const imageUrl =
              shopPagePhoto && shopPagePhoto.mediaUrl
                ? UMBRACO_ADRESS + shopPagePhoto.mediaUrl
                : "/placeholder.jpg";

            return (
              <Link to={`/photos-page/${item.id}`} key={item.id}>
                <div className={styles.photoCard}>
                  <div className={styles.photoImageContainer}>
                    <img
                      src={imageUrl}
                      alt={name}
                      className={styles.photoImage}
                    />
                    <div
                      className={`${styles.label} ${
                        item.published ? styles.published : ""
                      }`}
                    >
                      <p>{item.published ? "Published" : "Unpublished"}</p>
                    </div>
                  </div>
                  <div
                    className={`${styles.status} ${styles[item.status]}`}
                  ></div>
                  <div className={styles.photoDetailsContainer}>
                    <div className={styles.photoDetails}>
                      <h3>{name}</h3>
                      <p>Views: {item.views || 0}</p>
                      <p>Clicks: {item.clicks || 0}</p>
                    </div>
                    <div
                      className={styles.favButton}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleToggleFavorite(item.id, index, isFavorite);
                      }}
                    >
                      {isFavorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
        {/* Renderuj przycisk "MORE" tylko jeśli pozostały jeszcze elementy do wyświetlenia */}
        {visibleCount < sortedData.length && (
          <div className={styles.loadMore} onClick={handleLoadMore}>
            <p>MORE</p>
            <KeyboardArrowDownIcon />
          </div>
        )}
      </div>
    </div>
  );
};

export default PhotoGrid;
