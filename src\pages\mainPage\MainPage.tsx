import React, {useCallback, useEffect, useMemo, useRef, useState,} from "react";
import {Box} from "@mui/material";
import styles from "./mainPage.module.scss";
import InstructionPopup from "../../components/models/Instructions";
import UmbracoSyncModal from "../../components/umbracoSyncModal/UmbracoSyncModal";
import {addAlphaToHexColor} from "../../middleware/addAlphaToHexColor";
import {VideoPlayerContainer} from "../../components/videoPlayerContainer/VideoPlayerContainer";
import {ControlPanel} from "../../components/controlPanel/ControlPanel";
import useGetMovieById from "../../api/useGetMoviesById";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import DeleteConfirmationModal from "../../components/deleteConfirmationModal/DeleteConfirmationModal";
import {useGetVMStatus} from "../../api/useGetVMStatus";
import {useStartVM} from "../../api/useStartVM";
import {useStopVM} from "../../api/useStopVM";
import type {FormData} from "../../types/formData";
import useSyncWithUmbraco from "../../api/useSyncWithUmbraco";
import Loader from "../../components/loader/Loader";
import Header from "../../components/shared/Header";
import MovieEditorItemList from "../../components/movieEditorItemList/MovieEditorItemList";
import {useNavigate, useParams} from "react-router-dom";
import useDeleteDocument from "../../api/useDeleteDocument";
import {useQueryClient} from "@tanstack/react-query";
import useUpdateVideoDocument from "../../api/useUpdateVideoDocument";
import usePublishDocument from "../../api/usePublishDocument";
import useUnpublishDocument from "../../api/useUnpublishDocument";
import {useSaveVideo} from "../../api/useSaveVideoML";
import {SessionState} from "../../enums/sessionState";

import {maskColors} from "../../constants/colors";

import {connectWebSocket} from "../../websockets/wsClient";
import {setupWebSocketListeners} from "../../websockets/wsListeners";

//imports for websockets messages api - can be also imported and used in correct modules - to do later
import {useGetAllObjects} from "../../api/websockets/useGetAllObjects";
import {useAddItem} from "../../api/websockets/useAddItem";
import {useAddPoint} from "../../api/websockets/useAddPoint";
import {useRemovePoint} from "../../api/websockets/useRemovePoint";
import {useGetFrame} from "../../api/websockets/useGetFrame";
import {useGetFramesBatch} from "../../api/websockets/useGetFramesBatch";
import {useUpdateObject} from "../../api/websockets/useUpdateObject";
import {useRemoveObject} from "../../api/websockets/useRemoveObject";
import {useStopTracking} from "../../api/websockets/useStopTracking";
import {useStartTracking} from "../../api/websockets/useStartTracking";
import {useGetAllPoints} from "../../api/websockets/useGetAllPoints";
import {useGetState} from "../../api/websockets/useGetState";
import {useSaveState} from "../../api/websockets/useSaveState";
import {useGetVideoMetadata} from "../../api/websockets/useGetVideoMetadata";

import {useFormItems} from "../../api/useFormItems";
import {useSelectionPoints} from "../../api/useSelectionPoints";
import {useTrackingResults} from "../../api/useTrackingResults";
import {ObjectResult, Polygon} from "../../types/polygons.type";
import useAuth from "../../api/useAuth";
import { UMBRACO_ADRESS } from "../../constants/urls";

export const buttonStyles = {
  backgroundColor: "#F2F2F2",
  border: "2px solid #7E7979",
  color: "#000",
  py: 1,
  borderRadius: 2,
  fontWeight: "bold",
  "&:hover": {
    backgroundColor: "transparent",
    color: "#7E7979",
  },
  "&.Mui-disabled": {
    backgroundColor: "#F2F2F2",
    color: "#000",
  },
};

const MainPage = () => {
  const navigate = useNavigate();
  const {
    deleteDocument,
    loading: deleteLoading,
    error: deleteError,
  } = useDeleteDocument();
  const {
    publishDocument,
    loading: publishLoading,
    error: publishError,
  } = usePublishDocument();
  const {
    unpublishDocument,
    loading: unpublishLoading,
    error: unpublishError,
  } = useUnpublishDocument();
  const { mutate } = useSyncWithUmbraco();
  const queryClient = useQueryClient();
  const [errorState, setErrorState] = useState<string | null>(null);
  const playerRef = useRef<any>(null);
  const videoDimensionsRef = useRef({ width: 0, height: 0 });
  const videoCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const videoRef = useRef<any>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const isPlayingRef = useRef<boolean>(false);
  const wasPlayingBeforeMissing = useRef(false);
  const [currentFrame, setCurrentFrame] = useState<number>(0);
  const currentFrameRef = useRef<number>(0);
  const sessionIdRef = useRef<string>("");
  const effectRan = useRef(false);
  const boxRef = useRef<HTMLDivElement>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [videoUrl, setVideoUrl] = useState<string>();
  const [activeItem, setActiveItem] = useState(0);
  const [label, setLabel] = useState<boolean>(true);
  const [hoveredMarker, setHoveredMarker] = useState<string | null>(null);
  const [videoLoading, setVideoLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false);
  const { id } = useParams();
  const { data, error } = useGetMovieById(id ?? "");
  const [sessionId, setSessionId] = useState("");
  const [rFrameRate, setRFrameRate] = useState<number>(25);
  const rFrameRateRef = useRef<number>(25);
  const [posterImageUrls, setPosterImageUrls] = useState<{
    [key: number]: string;
  }>({});
  const [alertMessage, setAlertMessage] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);
  const [isPublished, setIsPublished] = useState(false);

  const [waitingForFrame, setWaitingForFrame] = useState<number | null>(null);
  const waitingForFrameRef = useRef<number | null>(null);
  const [lastDownloadedFrame, setLastDownloadedFrame] = useState<number | null>(null);
  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);

  const [sessionState, setSessionState] = useState<SessionState>(SessionState.INITIALIZING);
  const sessionStateRef = useRef<SessionState>(SessionState.INITIALIZING);
  const { token, fetchToken } = useAuth();

  const [formData, setFormData] = useState<FormData>({
    date: "",
    title: "",
    client: "",
    videographer: "",
    items: []
  })

  const { items, updateItem, addItem, removeItem, setItemImage, replaceItems } = useFormItems(formData.items);
  const { selectionPoints, selectionPointsRef, setSelectionPoints, addSelectionPoint, removeSelectionPoint, removeSelectionPointsForObject, replaceSelectionPoints} = useSelectionPoints();
  const { trackingResultsRef, trackingResults, setTrackingResult, setBatchTrackingResults, setTrackingResults, deleteObjectFromTrackingResults } = useTrackingResults();

  useEffect(() => {
    setFormData(prev => ({ ...prev, items }));
  }, [items]);

  const {
    data: vmStatus,
    isLoading: vmIsLoading,
    error: vmError,
  } = useGetVMStatus();
  const startMutation = useStartVM();
  const stopMutation = useStopVM();
  const {
    updateVideoDocument,
    loading: updateLoading,
    error: updateError,
    success: updateSuccess,
  } = useUpdateVideoDocument();
  const { saveVideo, isLoading } = useSaveVideo();

  // constants for sending websocket messages
  const { sendAddObject } = useAddItem();
  const { sendGetAllObjects } = useGetAllObjects();
  const { sendGetFrame } = useGetFrame();
  const { sendAddPoint } = useAddPoint();
  const { sendRemoveObject } = useRemoveObject();
  const { sendRemovePoint } = useRemovePoint();
  const { sendGetFramesBatch } = useGetFramesBatch();
  const { sendStartTracking } = useStartTracking();
  const { sendStopTracking } = useStopTracking();
  const { sendUpdateObject } = useUpdateObject();
  const { sendGetAllPoints } = useGetAllPoints();
  const { sendGetState } = useGetState();
  const { sendSaveState } = useSaveState();
  const { sendGetVideoMetadata } = useGetVideoMetadata();

  useEffect(() => {
    if (id) {
      setSessionId(id);
    }
    setIsPublished(data?.variants[0].state === "Published");
  }, [id, data]);

  useEffect(() => {
    queryClient.invalidateQueries({ queryKey: ["umbracoMedia"] }).catch(console.error);
  }, [queryClient]);

  // const updateFormData = () => {
  //   if (data && data.values) {
  //     const dateField = data.values.find((field: any) => field.alias === "date");
  //     const titleField = data.values.find((field: any) => field.alias === "pageTitle");
  //     const clientField = data.values.find((field: any) => field.alias === "client");
  //     const videographerField = data.values.find((field: any) => field.alias === "videographer");
  //     const addItemField = data.values.find((field: any) => field.alias === "addItem");

  //     setFormData((prev) => ({
  //       ...prev,
  //       date: dateField ? dateField.value : "",
  //       title: titleField ? titleField.value : "",
  //       client: clientField ? clientField.value : "",
  //       videographer: videographerField ? videographerField.value : "",
  //       items: addItemField
  //         ? addItemField.value.contentData.map((item: any, index: number) => {
  //           const mappedItem = item.values.reduce(
  //             (acc: any, cur: any) => {
  //               if (cur.alias === "minatureImage") {
  //                 acc.image = Array.isArray(cur.value) ? cur.value.join(", ") : cur.value;
  //               } else if (cur.alias === "affiliateLink") {
  //                 acc.link = cur.value;
  //               } else if (cur.alias === "productName") {
  //                 acc.itemName = cur.value;
  //               } else if (cur.alias === "displayedName") {
  //                 acc.identificationName = cur.value;
  //               } else if (cur.alias === "brand") {
  //                 acc.brand = cur.value;
  //               }
  //               return acc;
  //             },
  //             { id: index, image: "", itemName: "", brand: "", link: "", identificationName: "" }
  //           );
  //           return mappedItem;
  //         })
  //         : [],
  //     }));
  //   }
  // };

  // useEffect(() => {
  //   updateFormData();
  // }, [data]);

  useEffect(() => {
    if (data && data.values) {
      const videoField = data.values.find(
        (field: any) => field.alias === "video"
      );
      if (videoField && videoField.value && videoField.value.src) {
        let src = videoField.value.src;
        if (src.startsWith("/")) {
          src = src.slice(1);
        }
        setVideoUrl(src);
      }
    }
  }, [data]);

  const showAlert = (type: "success" | "error", message: string) => {
    setAlertMessage({ type, message });
    setTimeout(() => setAlertMessage(null), 3000);
  };

  const handleDelete = async () => {
    if (id) {
      setLoading(true);
      setIsDeleteModalOpen(false);
      await deleteDocument(id);
      queryClient.invalidateQueries({ queryKey: ["umbracoMediaVideo"] });
      navigate("/clique");
      setLoading(false);
    } else {
      setErrorState("Document ID missing");
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    const updateData = {
      date: formData.date,
      pageTitle: formData.title,
      client: formData.client,
      videographer: formData.videographer,
      video: videoUrl!,
      addItem: formData.items.map((item) => ({
        minatureImage: item.image || null,
        affiliateLink: item.link || "",
        productName: item.itemName || "",
        displayedName: item.identificationName || "",
        brand: item.brand || "",
      })),
    };

    const updateDataML = {
      type: "",
      objects: formData.items.map((item) => ({
        objectId: item.id,
        url: item.link || "",
        brand: item.brand || "",
        idName: item.identificationName || "",
        name: item.itemName || "",
        image: item.image || null,
      })),
    };

    if (id) {
      let jsonResponse = await saveVideo(sessionId, updateDataML);

      // build the body:
      const temporaryFileId = await uploadJsonFile(jsonResponse);
      await updateVideoDocument(id, jsonResponse, temporaryFileId, updateData);
      sendSaveState();
      queryClient.invalidateQueries({ queryKey: ["umbracoMediaVideo"] });
      navigate("/clique");
    } else {
      console.error("Document ID is missing.");
    }
    setLoading(false);
  };

  async function uploadJsonFile(
    input: any,
    uploadUrl = `${UMBRACO_ADRESS}umbraco/management/api/v1/temporary-file`
  ): Promise<string> {
    const jsonString = JSON.stringify(input);
    const blob = new Blob([jsonString], { type: "application/json" });
    const formData = new FormData();
    let tempfileid = crypto.randomUUID();
    formData.append("Id", tempfileid);
    formData.append("File", blob, `payload_${Date.now()}.json`);

    const res = await fetch(uploadUrl, { method: "POST", body: formData, headers: { Authorization: `Bearer ${token}` } });
    if (!res.ok) {
      throw new Error(`Upload failed: ${res.status} ${res.statusText}`);
    }
    return tempfileid;
  }

  const handlePublish = async () => {
    setLoading(true);
    if (!id) {
      setErrorState("No ID to publish.");
      return;
    }
    await publishDocument(id);
    showAlert("success", "Document unpublished successfully!");
    window.location.reload();
    setLoading(false);
  };

  const handleUnpublish = async () => {
    setLoading(true);
    if (!id) {
      setErrorState("No ID to unpublish.");
      return;
    }
    await unpublishDocument(id);
    showAlert("success", "Document unpublished successfully!");
    window.location.reload();
    setLoading(false);
  };

  const handleSyncWithUmbraco = (id: string) => {
    setLoading(true);
    mutate(
      { sessionId: sessionIdRef.current, pageId: id },
      {
        onSuccess: () => {
          setLoading(false);
        },
        onError: (error) => {
          setLoading(false);
          setErrorState(`Error during synchronization: ${error}`);
        },
      }
    );
  };

  const handleToggleVM = async () => {
    if (vmStatus) {
      stopMutation.mutate();
    } else {
      const response = startMutation.mutate();
      console.log(response);
    }
  };

  const handleReady = (player: any) => {
  };

  useEffect(() => {
    console.log("Updated sessionState from state:", sessionState);
  }, [sessionState]);

  const drawPolygonOnCanvas = (
    polygon: Polygon,
    canvas: HTMLCanvasElement,
    color: string,
    ctx: CanvasRenderingContext2D
  ) => {

    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;

    const scaledPolygon = polygon.points.map(({ x, y }) => ({
      x: x * canvasWidth,
      y: y * canvasHeight,
    }));

    ctx.fillStyle = addAlphaToHexColor(color, 0.5);
    ctx.strokeStyle = color || "rgba(0, 255, 0, 1)";
    ctx.lineWidth = 2;

    ctx.beginPath();
    scaledPolygon.forEach(({ x, y }, index) => {
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
  };

  // Cache for rendered frames to improve performance
  const renderedFramesCache = useRef<Map<number, ImageData>>(new Map());
  const lastDrawnFrame = useRef<number>(-1);

  const drawFrame = useCallback(() => {
    const canvas = videoCanvasRef.current;
    if (!canvas) return;

    const { width, height } = canvas.getBoundingClientRect();

    // Only resize canvas if dimensions have changed
    if (canvas.width !== width || canvas.height !== height) {
      canvas.width = width;
      canvas.height = height;
      // Clear cache when canvas size changes
      renderedFramesCache.current.clear();
    }

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const currentFrame = currentFrameRef.current;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    const frameResults = trackingResultsRef.current[currentFrame];
    if (!frameResults || frameResults.length === 0) {
      lastDrawnFrame.current = currentFrame;
      return;
    }

    // Draw the current frame
    frameResults.forEach((objectResult: ObjectResult, index: number) => {
      const maskColor = maskColors[index] || "#00FF00";

      objectResult.polygons.forEach((polygon) => {
        drawPolygonOnCanvas(polygon, canvas, maskColor, ctx);
      });
    });

    // Cache the current frame
    try {
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      renderedFramesCache.current.set(currentFrame, imageData);

      // Limit cache size to avoid memory issues
      if (renderedFramesCache.current.size > 30) {
        // Remove oldest entries
        const keysToDelete = Array.from(renderedFramesCache.current.keys())
          .sort((a, b) => a - b)
          .slice(0, 10);

        keysToDelete.forEach(key => {
          renderedFramesCache.current.delete(key);
        });
      }
    } catch (e) {
      console.warn("Failed to cache frame:", e);
    }

    lastDrawnFrame.current = currentFrame;

    // Pre-render next frames for smoother playback if we're playing
    if (isPlayingRef.current) {
      const nextFrames = [1, 2, 3].map(offset => currentFrame + offset);
      nextFrames.forEach(nextFrame => {
        const nextFrameResults = trackingResultsRef.current[nextFrame];
        if (nextFrameResults && !renderedFramesCache.current.has(nextFrame)) {
          // Request the next frame data if not already available
          if (!requestedFrames.current.has(nextFrame)) {
            requestedFrames.current.add(nextFrame);
            sendGetFrame(nextFrame).catch(console.error);
          }
        }
      });
    }
  }, [trackingResultsRef, currentFrameRef, sendGetFrame, isPlayingRef]);

  const requestedFrames = useRef<Set<number>>(new Set());

  useEffect(() => {
    console.log("Updated rFrameRate:", rFrameRate);
  }, [rFrameRate])

  useEffect(() => {
    if (!isWebSocketConnected) return;
    const frameResults = trackingResultsRef.current[currentFrameRef.current];

    if (frameResults === undefined && !requestedFrames.current.has(currentFrameRef.current)) {
      requestedFrames.current.add(currentFrameRef.current);

      wasPlayingBeforeMissing.current = isPlayingRef.current;
      setWaitingForFrame(currentFrameRef.current);
      waitingForFrameRef.current = currentFrameRef.current;
      sendGetFrame(currentFrameRef.current).catch(console.error);
    } else if (frameResults !== undefined) {
      requestAnimationFrame(drawFrame);

      if (waitingForFrameRef.current  === currentFrameRef.current) {
        playerRef.current?.removeClass('vjs-waiting');
        playerRef.current?.controls(true);
        if (wasPlayingBeforeMissing.current) {
          playerRef.current?.play();
        }
        setWaitingForFrame(null);
        waitingForFrameRef.current = null;
      }
    }
  }, [sendGetFrame, drawFrame, trackingResultsRef, isWebSocketConnected]);

  const handleProgress = useCallback(
      (state: { playedSeconds: number }) => {
        if (sessionStateRef.current === SessionState.TRACKING) return;

        const frame = Math.floor(state.playedSeconds * rFrameRateRef.current);

        setCurrentFrame(prevFrame => {
          if (prevFrame !== frame) {
            return frame;
          }
          return prevFrame;
        });
      },
      []
  );

  useEffect(() => {
    currentFrameRef.current = currentFrame;

    if (trackingResultsRef.current[currentFrame]) {
      requestAnimationFrame(drawFrame);
    }
  }, [currentFrame, drawFrame, trackingResultsRef]);

  const requestedBatchStarts = useRef<Set<number>>(new Set());


  useEffect(() => {
    if (sessionStateRef.current === SessionState.TRACKING) return;
    if (!isWebSocketConnected) return;
    if (waitingForFrameRef.current !== null) return;

    const startFrame = Math.floor(currentFrameRef.current / 25) * 25;

    // Increase preload range when playing to ensure smoother playback
    const preloadEndFrame = isPlayingRef.current
      ? currentFrameRef.current + 200  // More aggressive preloading when playing
      : currentFrameRef.current + 100; // Standard preloading when paused

    // Use smaller batch size for more frequent updates
    const batchSize = isPlayingRef.current ? 15 : 25;

    for (let batchStart = startFrame; batchStart <= preloadEndFrame; batchStart += batchSize) {
      if (!requestedBatchStarts.current.has(batchStart)) {
        requestedBatchStarts.current.add(batchStart);
        sendGetFramesBatch(batchStart, batchSize).catch(console.error);
      }
    }
  }, [currentFrame, sendGetFramesBatch, isWebSocketConnected, isPlayingRef]);

  const wsContext = useMemo(() => ({
    sessionStateRef,
    sessionState,
    setSessionState,
    setTrackingResult,
    setTrackingResults,
    trackingResultsRef,
    setBatchTrackingResults,
    setActiveItem,
    setLabel,
    updateItem,
    addItem,
    removeItem,
    setItemImage,
    replaceItems,
    addSelectionPoint,
    setSelectionPoints,
    removeSelectionPoint,
    removeSelectionPointsForObject,
    replaceSelectionPoints,
    drawFrame,
    currentFrameRef,
    setCurrentFrame,
    setRFrameRate,
    rFrameRateRef,
    playerRef,
    sendGetFrame,
    setLastDownloadedFrame,
    showError: (msg: string) => setErrorState(msg),
    sendGetFramesBatch,
    deleteObjectFromTrackingResults
  }), [
    sessionState,
    setSessionState,
    sessionStateRef,
    setTrackingResult,
    setTrackingResults,
    trackingResultsRef,
    setBatchTrackingResults,
    setActiveItem,
    setLabel,
    updateItem,
    addItem,
    removeItem,
    setItemImage,
    replaceItems,
    addSelectionPoint,
    setSelectionPoints,
    removeSelectionPoint,
    removeSelectionPointsForObject,
    replaceSelectionPoints,
    drawFrame,
    currentFrameRef,
    setCurrentFrame,
    setRFrameRate,
    rFrameRateRef,
    sendGetFrame,
    setLastDownloadedFrame,
    playerRef,
    sendGetFramesBatch,
    deleteObjectFromTrackingResults
  ]);

  const WebsocketOpen = useCallback(() => {
    setIsWebSocketConnected(true);
  }, []);

  useEffect(() => {
    if (sessionId && videoUrl && !effectRan.current) {
      const createSession = async () => {
        try {
          if (!effectRan.current) {
            connectWebSocket("test", WebsocketOpen);
            setupWebSocketListeners(wsContext);
            effectRan.current = true;
          }

          await sendGetVideoMetadata();
          await sendGetAllObjects();
          await sendGetAllPoints();
          await sendGetFrame(currentFrame);
          await sendGetState();

          setVideoLoading(false);
        } catch (err) {
          console.error("Failed to create session:", err);
          setErrorState("Failed to create session.");
        }
      };
      createSession().catch(console.error);
      effectRan.current = true;
    }
  }, [sessionId, videoUrl, vmStatus, wsContext, sendGetAllObjects, sendGetAllPoints, sendGetFrame, currentFrame, sendGetState, WebsocketOpen, sendGetVideoMetadata]);

  const handleVideoClick = async (
    event: React.MouseEvent<HTMLCanvasElement>,
    label: number
  ) => {
    const canvas = videoCanvasRef.current;
    if (!canvas) {
      console.warn("Canvas element not found.");
      return;
    }

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;


    const normalizedX = x / rect.width;
    const normalizedY = y / rect.height;

    const currentDetection = items.find(
      (obj) => obj.id === activeItem
    );
    if (!currentDetection) {
      console.error("No active detection object found.");
      return;
    }

    const frame = Math.floor(playerRef.current.currentTime() * rFrameRateRef.current);

    console.log("Sending add point")
    await sendAddPoint(
        {
          frameIndex: frame,
          objectId: currentDetection.id,
          label: label,
          coordinates: {
            x: normalizedX,
            y: normalizedY,
          }
        }
    )
  };

  const handleSwitchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsPublished(event.target.checked);
    if (event.target.checked) {
      handlePublish();
    } else {
      handleUnpublish();
    }
  };

  const canAddNewObject = items.length < 100

  const hasTrackingResults = useCallback((frame: number) => {
    return !!trackingResultsRef.current[frame];
  }, [trackingResultsRef]);

  // The logic for waitingForFrame and drawing is now handled in a combined useEffect above.


  return (
    <>
      {alertMessage && (
        <MessageAlert type={alertMessage.type} message={alertMessage.message} />
      )}
      {errorState && <MessageAlert type="error" message={errorState} />}
      {error && <MessageAlert type="error" message="Error loading data" />}
      {deleteError && <MessageAlert type="error" message={deleteError} />}
      <Header />
      <Box px={5} pb={3}>
        <div className={styles.filters}>
          <div className={styles.switchesContainer}>
            <span className={styles.label}>UNPUBLISH</span>
            <label className={styles.switch}>
              <input
                className={styles.switchInput}
                type="checkbox"
                checked={isPublished}
                onChange={handleSwitchChange}
              />
              <span className={styles.switchSlider}></span>
            </label>
            <span className={styles.label}>PUBLISH</span>
          </div>
          <button
            className={`${styles.delete} ${styles.button}`}
            onClick={() => setIsDeleteModalOpen(true)}
          >
            Delete
          </button>
          <button
            className={`${styles.save} ${styles.button}`}
            onClick={handleSave}
          >
            Save
          </button>
        </div>
        <Box display="flex" gap={5} sx={{ width: "100%", height: "100%" }}>
          <ControlPanel
            sessionState={sessionState}
            sendStartTracking={sendStartTracking}
            sendStopTracking={sendStopTracking}
            setIsSyncModalOpen={setIsSyncModalOpen}
            handleToggleVM={handleToggleVM}
            buttonStyles={buttonStyles}
            data={data}
            vmError={vmError}
            vmStatus={vmStatus}
            startMutation={startMutation}
            stopMutation={stopMutation}
            vmIsLoading={vmIsLoading}
            setFormData={setFormData}
            formData={formData}
            currentFrame={currentFrame}
          />
          <VideoPlayerContainer
            loading={videoLoading}
            sessionState={sessionState}
            sessionStateRef={sessionStateRef}
            selectionPointsRef={selectionPointsRef}
            currentFrame={currentFrame}
            currentFrameRef={currentFrameRef}
            boxRef={boxRef}
            videoCanvasRef={videoCanvasRef}
            svgRef={svgRef}
            videoRef={videoRef}
            playerRef={playerRef}
            videoUrl={videoUrl || ""}
            isPlayingRef={isPlayingRef}
            handleVideoClick={handleVideoClick}
            sendRemovePoint={sendRemovePoint}
            handleReady={handleReady}
            handleProgress={handleProgress}
            hasTrackingResult={hasTrackingResults}
            setWaitingForFrame={setWaitingForFrame}
            setHoveredMarker={setHoveredMarker}
            hoveredMarker={hoveredMarker}
            label={label}
          />
        </Box>
        <InstructionPopup />
        <MovieEditorItemList
          buttonStyles={buttonStyles}
          canAddNewObject={canAddNewObject}
          activeItem={activeItem}
          setActiveItem={setActiveItem}
          setLabel={setLabel}
          label={label}
          posterImageUrls={posterImageUrls}
          items={items}
          addItem={sendAddObject}
          updateItem={sendUpdateObject}
          removeItem={sendRemoveObject}
          setItemImage={setItemImage}
          handleSave={handleSave}
          sessionId={sessionId}
          sendRemovePoint={sendRemovePoint}
          selectionPointsRef={selectionPointsRef}
          currentFrame={currentFrame}
        />
      </Box>
      <UmbracoSyncModal
        open={isSyncModalOpen}
        onClose={() => setIsSyncModalOpen(false)}
        onSync={handleSyncWithUmbraco}
      />
      <DeleteConfirmationModal
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDelete}
      />
      {loading && <Loader />}
    </>
  );
};

export default MainPage;
