import styles from "./favesPage.module.scss";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import PhotoGrid from "../../components/photoGrid/PhotoGrid";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import Loader from "../../components/loader/Loader";
import { useEffect, useState } from "react";
import useGetShopPageDataWithFilters from "../../api/useGetShopPageDataWithFilters";
import useGetFavorites from "../../api/useGetFavorites";

const FavesPage = () => {
  const { data: fav } = useGetFavorites();
  const { data, isLoading, error } = useGetShopPageDataWithFilters();
  const [photos, setPhotos] = useState([]);
  useEffect(() => {
    if (data) {
      //TODO set only faves
      setPhotos(data);
    }
  }, [data]);

  if (isLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  return (
    <div className={styles.photosPage}>
      <SearchBar />
      <div className={styles.container}>
        <Sidebar />
        <PhotoGrid items={photos} />
      </div>
    </div>
  );
};

export default FavesPage;
