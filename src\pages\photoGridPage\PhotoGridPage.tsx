import { useMemo } from "react";
import useGetShopPageDataWithFilters from "../../api/useGetShopPageDataWithFilters";
import useGetFavorites from "../../api/useGetFavorites";
import PhotoGrid from "../../components/photoGrid/PhotoGrid";
import SearchBar from "../../components/searchBar/SearchBar";
import Sidebar from "../../components/sideBar/SideBar";
import Loader from "../../components/loader/Loader";
import MessageAlert from "../../components/messageAlert/MessageAlert";
import styles from "./photoGridPage.module.scss";

type Photo = {
  id: string;
  [key: string]: any;
};

const PhotoGridPage = () => {
  const {
    data: photosData,
    isLoading,
    error,
  } = useGetShopPageDataWithFilters();
  const { data: favoritesData, isLoading: isFavoritesLoading } =
    useGetFavorites();

  const enrichedPhotos = useMemo(() => {
    if (!photosData || !favoritesData) return [];

    const favoriteIds = new Set(
      favoritesData.map((fav: { key: string }) => fav.key)
    );

    return photosData.map((photo: Photo) => ({
      ...photo,
      isFavorite: favoriteIds.has(photo.id),
    }));
  }, [photosData, favoritesData]);

  if (isLoading || isFavoritesLoading) return <Loader />;
  if (error) return <MessageAlert type="error" message="Error loading data" />;

  return (
    <div className={styles.photosPage}>
      <SearchBar />
      <div className={styles.container}>
        <Sidebar />
        <PhotoGrid items={enrichedPhotos} />
      </div>
    </div>
  );
};

export default PhotoGridPage;
